<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Fix Test</title>
    <link rel="stylesheet" href="dashboard.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .test-controls {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }
        .test-btn {
            background: #8b5cf6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            margin: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-btn:hover {
            background: #7c3aed;
        }
        .test-status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 6px;
            font-weight: bold;
        }
        .success { background: rgba(16, 185, 129, 0.2); color: #10b981; }
        .error { background: rgba(239, 68, 68, 0.2); color: #ef4444; }
        .info { background: rgba(59, 130, 246, 0.2); color: #3b82f6; }
    </style>
</head>
<body class="dashboard-body">
    <div class="dashboard-container">
        <!-- Test Controls -->
        <div class="test-controls">
            <h2>🧪 Dashboard Fix Test Suite</h2>
            <p>Test all the dashboard fixes in one place</p>
            <button class="test-btn" onclick="addTestData()">
                <i class="fas fa-database"></i> Add Test Data
            </button>
            <button class="test-btn" onclick="testAllFixes()">
                <i class="fas fa-play"></i> Test All Fixes
            </button>
            <button class="test-btn" onclick="clearTestData()">
                <i class="fas fa-trash"></i> Clear Data
            </button>
            <div id="testStatus" class="test-status info">
                Click "Add Test Data" first, then "Test All Fixes"
            </div>
        </div>

        <!-- Dashboard Header -->
        <header class="dashboard-header">
            <div class="header-left">
                <button class="back-btn" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 id="roomTitle">Room: testroom</h1>
            </div>
            <div class="header-right">
                <div class="live-indicator">
                    <div class="pulse-dot"></div>
                    <span>LIVE</span>
                </div>
                <button class="refresh-btn" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </header>

        <!-- Dashboard Main Grid -->
        <main class="dashboard-main">
            <!-- Meeting Metrics -->
            <section class="dashboard-card">
                <div class="card-header">
                    <h2><i class="fas fa-chart-line"></i> Meeting Metrics</h2>
                </div>
                <div class="card-content">
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <div class="metric-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="metric-info">
                                <div class="metric-label">Duration</div>
                                <div class="metric-value" id="meetingDuration">00:00</div>
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="metric-info">
                                <div class="metric-label">Participants</div>
                                <div class="metric-value" id="participantCount">0</div>
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="metric-info">
                                <div class="metric-label">Avg Attention</div>
                                <div class="metric-value" id="avgAttention">—</div>
                            </div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-icon">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="metric-info">
                                <div class="metric-label">Engagement</div>
                                <div class="metric-value" id="engagementScore">—</div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Sentiment Analysis -->
            <section class="dashboard-card">
                <div class="card-header">
                    <h2><i class="fas fa-heart"></i> Sentiment Analysis</h2>
                    <div class="sentiment-indicator">
                        <span id="sentimentEmoji">😐</span>
                        <span id="overallSentiment">Neutral</span>
                    </div>
                </div>
                <div class="card-content">
                    <div class="chart-container">
                        <canvas id="sentimentChart"></canvas>
                    </div>
                    <div class="sentiment-stats">
                        <div class="stat-item">
                            <div class="stat-label">Positive</div>
                            <div class="stat-value positive" id="positivePercent">0%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Neutral</div>
                            <div class="stat-value neutral" id="neutralPercent">0%</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">Negative</div>
                            <div class="stat-value negative" id="negativePercent">0%</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- AI Summary -->
            <section class="dashboard-card summary-card">
                <div class="card-header">
                    <h2><i class="fas fa-robot"></i> AI Summary & Action Items</h2>
                    <button class="test-btn" id="generateSummaryBtn">
                        <i class="fas fa-magic"></i> Generate Summary
                    </button>
                </div>
                <div class="card-content">
                    <div id="summaryContent" class="summary-content">
                        <div class="placeholder">
                            <i class="fas fa-robot"></i>
                            <p>Click "Generate Summary" to create an AI-powered meeting summary with action items and key insights.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recent Transcript -->
            <section class="dashboard-card">
                <div class="card-header">
                    <h2><i class="fas fa-file-text"></i> Recent Transcript</h2>
                    <button class="test-btn" onclick="downloadTranscript()">
                        <i class="fas fa-download"></i> Download
                    </button>
                </div>
                <div class="card-content">
                    <div id="recentTranscript" class="recent-transcript">
                        <div class="placeholder">
                            <i class="fas fa-microphone"></i>
                            <p>No transcript data available yet.</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        const room = 'testroom';
        let sentimentChart = null;

        // Initialize sentiment chart
        function initializeSentimentChart() {
            const ctx = document.getElementById('sentimentChart').getContext('2d');
            
            sentimentChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Sentiment Score',
                        data: [],
                        borderColor: '#6366f1',
                        backgroundColor: 'rgba(99, 102, 241, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: { legend: { display: false } },
                    scales: {
                        y: { min: -1, max: 1, grid: { color: 'rgba(255,255,255,0.1)' } },
                        x: { grid: { color: 'rgba(255,255,255,0.1)' } }
                    }
                }
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', () => {
            initializeSentimentChart();
        });

        // Include the dashboard.js functions here
    </script>
    <script src="dashboard.js"></script>
    
    <script>
        // Test-specific functions
        async function addTestData() {
            const statusEl = document.getElementById('testStatus');
            statusEl.className = 'test-status info';
            statusEl.textContent = 'Adding test data...';
            
            try {
                const response = await fetch(`/test-data/${room}`, { method: 'POST' });
                const data = await response.json();
                
                if (response.ok) {
                    statusEl.className = 'test-status success';
                    statusEl.textContent = `✅ Test data added: ${data.participants} participants, ${data.transcript_entries} messages`;
                } else {
                    statusEl.className = 'test-status error';
                    statusEl.textContent = `❌ Failed to add test data: ${data.error}`;
                }
            } catch (error) {
                statusEl.className = 'test-status error';
                statusEl.textContent = `❌ Error: ${error.message}`;
            }
        }

        async function testAllFixes() {
            const statusEl = document.getElementById('testStatus');
            statusEl.className = 'test-status info';
            statusEl.textContent = 'Testing all fixes...';
            
            // Refresh dashboard data
            await loadDashboardData();
            
            // Wait a moment for updates
            setTimeout(() => {
                statusEl.className = 'test-status success';
                statusEl.textContent = '✅ All fixes tested! Check the dashboard components above.';
            }, 1000);
        }

        async function clearTestData() {
            const statusEl = document.getElementById('testStatus');
            statusEl.className = 'test-status info';
            statusEl.textContent = 'Test data cleared (refresh page to see changes)';
        }

        function goBack() {
            window.history.back();
        }
    </script>
</body>
</html>
