/* Enhanced CSS with all styling improvements */
:root {
  --primary: #6366f1;
  --primary-dark: #4f46e5;
  --primary-light: #818cf8;
  --secondary: #06b6d4;
  --secondary-dark: #0891b2;
  --success: #10b981;
  --warning: #f59e0b;
  --danger: #ef4444;
  --info: #3b82f6;

  --bg: #0f172a;
  --bg-light: #1e293b;
  --surface: #1e293b;
  --surface-light: #334155;
  --surface-lighter: #475569;
  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-muted: #64748b;

  --muted: #6b7280;
  --accent: #6366f1;
  --accent-2: #06b6d4;
  --glass: rgba(255,255,255,0.06);
  --shadow: 0 6px 24px rgba(12,20,30,0.08);
  --radius: 12px;
  --max-width: 1200px;
  --gap: 18px;
  --tile-bg: #0b1020;
}

/* --- base layout --- */
* { box-sizing: border-box; }
html,body { height:100%; margin:0; font-family: Inter,ui-sans-serif,system-ui,-apple-system,"Segoe UI",Roboto,"Helvetica Neue",Arial; background: linear-gradient(180deg,#071020 0%, #0f1724 100%); color: #e6eef8; -webkit-font-smoothing:antialiased; -moz-osx-font-smoothing:grayscale; }
a { color:var(--accent); text-decoration:none; }
.app { max-width: var(--max-width); margin:28px auto; padding:20px; }

/* --- topbar --- */
.topbar { display:flex; align-items:center; justify-content:space-between; gap:20px; padding:14px; border-radius:14px; background:linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01)); box-shadow: var(--shadow); }
.brand { display:flex; gap:12px; align-items:center; }
.logo { width:44px; height:44px; color: var(--accent-2); }
.title { font-weight:700; font-size:16px; color:#fff; }
.subtitle { font-size:12px; color:var(--muted); margin-top:2px; }

/* --- actions --- */
.actions { display:flex; gap:10px; align-items:center; }
.room-input { padding:10px 12px; border-radius:10px; border:1px solid rgba(255,255,255,0.04); background:transparent; color:inherit; width:220px; outline:none; }
.room-input::placeholder { color: rgba(230,238,248,0.45); }

.btn { padding:10px 14px; border-radius:10px; border:0; cursor:pointer; font-weight:600; }
.btn.primary { background: linear-gradient(90deg,var(--accent), #8b5cf6); color:#fff; box-shadow: 0 6px 20px rgba(99,102,241,0.14); }
.btn.outline { background:transparent; border:1px solid rgba(255,255,255,0.06); color:#fff; }
.btn.dashboard { background: linear-gradient(90deg, var(--secondary), var(--primary)); color:#fff; box-shadow: 0 6px 20px rgba(6,182,212,0.14); transition: all 0.3s ease; }
.btn.dashboard:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(6,182,212,0.2); }

/* --- main layout --- */
.main { display:grid; grid-template-columns: 1fr 320px; gap:var(--gap); margin-top:18px; }
.stage { background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01)); padding:18px; border-radius:12px; box-shadow:var(--shadow); min-height:420px; display:flex; flex-direction:column; gap:12px; }
.sidebar { display:flex; flex-direction:column; gap:14px; }

/* header inside stage */
.grid-header { display:flex; justify-content:space-between; align-items:center; gap:10px; }
.grid-header h2 { margin:0; color:#fff; font-size:18px; }
.grid-header .stats { color:var(--muted); font-size:13px; display:flex; gap:12px; align-items:center; }

/* --- video grid --- */
.video-grid { display:grid; grid-template-columns: repeat(auto-fit, minmax(240px, 1fr)); gap:16px; align-items:start; }
.video-tile { position:relative; background:var(--tile-bg); border-radius:12px; overflow:hidden; min-height:160px; display:flex; align-items:center; justify-content:center; aspect-ratio:16/9; box-shadow: 0 10px 30px rgba(2,6,23,0.6) inset; border: 1px solid rgba(255,255,255,0.03); }
.video-tile video { width:100%; height:100%; object-fit:cover; display:block; background:linear-gradient(180deg,#000,#05060a); }

/* local tile styling */
.local-tile { outline: 2px solid rgba(99,102,241,0.06); }

/* tile footer (name / badges) */
.tile-footer { position:absolute; left:12px; right:12px; bottom:12px; display:flex; justify-content:space-between; align-items:center; gap:8px; background: linear-gradient(180deg, rgba(0,0,0,0.08), rgba(0,0,0,0.18)); padding:8px 10px; border-radius:10px; }
.tile-footer .name { font-weight:700; font-size:13px; color:#fff; }
.tile-footer .meta { font-size:12px; color:var(--muted); }
.badge { display:inline-block; padding:4px 8px; border-radius:999px; background:rgba(255,255,255,0.04); font-size:12px; color:#dbeafe; }
.badge.muted { background: rgba(255,255,255,0.02); color:var(--muted); }

/* --- participants list / sidebar cards --- */
.card { background:linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01)); padding:12px; border-radius:12px; box-shadow:var(--shadow); }
.card.small { padding:10px; font-size:13px; color:var(--muted); }
.participants { list-style:none; padding:0; margin:8px 0 0 0; display:flex; flex-direction:column; gap:8px; }
.participants li { display:flex; gap:10px; align-items:center; padding:8px; border-radius:8px; background:rgba(255,255,255,0.01); }
.participants li .dot { width:10px; height:10px; border-radius:50%; background:var(--accent); }
.tips { margin:0; padding-left:16px; color:var(--muted); }

/* --- log area --- */
.log pre { background: rgba(0,0,0,0.35); color: #dff1ff; padding:10px; border-radius:8px; max-height:140px; overflow:auto; margin:0; font-size:13px; }

/* --- footer --- */
.footer { margin-top:16px; display:flex; justify-content:space-between; color:var(--muted); font-size:13px; align-items:center; }

/* --- Quick Actions --- */
.quick-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin: 16px 0;
  padding: 12px;
  background: linear-gradient(180deg, rgba(255,255,255,0.02), rgba(255,255,255,0.01));
  border-radius: 12px;
  box-shadow: var(--shadow);
}

.action-btn {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: var(--surface-light);
  color: var(--text-muted);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-size: 18px;
}

.action-btn:hover {
  background: var(--surface-lighter);
  color: var(--text-secondary);
  transform: translateY(-2px);
}

.action-btn.active {
  background: var(--primary);
  color: white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}

/* --- Tab Navigation --- */
.tab-navigation {
  display: flex;
  background: var(--surface);
  border-radius: 8px;
  padding: 4px;
  margin-bottom: 16px;
  gap: 4px;
}

.tab-btn {
  flex: 1;
  padding: 8px 12px;
  border: none;
  background: transparent;
  color: var(--text-muted);
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.tab-btn:hover {
  background: rgba(255,255,255,0.05);
  color: var(--text-secondary);
}

.tab-btn.active {
  background: var(--primary);
  color: white;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* --- Enhanced Video Tiles --- */
.tile-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.7));
  padding: 12px;
  color: white;
}

.participant-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.participant-info .name {
  font-weight: 600;
  font-size: 14px;
}

.participant-info .status {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.attention-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  color: var(--accent-2);
}

/* --- Stats Grid --- */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
  margin-top: 12px;
}

.stat-item {
  text-align: center;
  padding: 8px;
  background: rgba(255,255,255,0.02);
  border-radius: 8px;
}

.stat-label {
  font-size: 11px;
  color: var(--text-muted);
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

/* --- Leaderboard --- */
.leaderboard {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 12px;
}

.leaderboard-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: rgba(255,255,255,0.02);
  border-radius: 8px;
  border-left: 3px solid var(--primary);
}

.leaderboard-rank {
  display: flex;
  align-items: center;
  gap: 10px;
}

.rank-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.leaderboard-score {
  font-weight: 600;
  color: var(--accent-2);
}

/* --- responsive --- */
@media (max-width: 980px) {
  .main { grid-template-columns: 1fr; }
  .sidebar { order: 2; }
  .stage { order: 1; }
  .video-grid { grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); }
  .actions { flex-wrap:wrap; gap:8px; }
  .room-input { width:150px; }
  .quick-actions { margin: 12px 0; }
  .action-btn { width: 44px; height: 44px; font-size: 16px; }
}

/* --- Transcript Container --- */
.transcript-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 12px;
  background: rgba(0,0,0,0.2);
  border-radius: 8px;
  margin-top: 12px;
}

.transcript-entry {
  margin-bottom: 12px;
  padding: 8px;
  background: rgba(255,255,255,0.02);
  border-radius: 6px;
  border-left: 3px solid var(--text-muted);
}

.transcript-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
  color: var(--text-muted);
}

.transcript-text {
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-secondary);
}

/* --- Interim Transcript (Live Speech Recognition) --- */
.interim-transcript {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
  border: 1px solid rgba(99, 102, 241, 0.3);
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  font-style: italic;
  color: #818cf8;
  position: relative;
  overflow: hidden;
}

.interim-transcript::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
  animation: shimmer 2s infinite;
}

.interim-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--primary-light);
  margin-bottom: 6px;
  font-weight: 600;
}

.interim-header i {
  animation: pulse 1.5s infinite;
}

.interim-text {
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-primary);
  font-style: italic;
}

/* --- Sentiment-based transcript styling --- */
.transcript-entry.sentiment-positive {
  border-left-color: var(--success);
}

.transcript-entry.sentiment-negative {
  border-left-color: var(--danger);
}

.transcript-entry.sentiment-neutral {
  border-left-color: var(--text-muted);
}

/* --- Enhanced Quick Action Button States --- */
.action-btn.active {
  background: var(--primary);
  color: white;
  box-shadow: 0 0 20px rgba(99, 102, 241, 0.4);
}

.action-btn.active i {
  animation: pulse 2s infinite;
}

/* --- Animations --- */
@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

@keyframes flash {
  0%, 100% { background-color: transparent; }
  50% { background-color: rgba(99, 102, 241, 0.1); }
}

/* --- Transcript Controls --- */
.transcript-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.speech-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--text-muted);
  padding: 4px 8px;
  border-radius: 4px;
  background: rgba(0,0,0,0.2);
  transition: all 0.3s ease;
}

.speech-status.active {
  color: var(--success);
  background: rgba(16, 185, 129, 0.1);
}

.speech-status.active i {
  animation: pulse 1.5s infinite;
}

.speech-status.error {
  color: var(--danger);
  background: rgba(239, 68, 68, 0.1);
}

/* --- Summary Container --- */
.summary-container {
  margin-top: 12px;
  padding: 12px;
  background: rgba(0,0,0,0.2);
  border-radius: 8px;
  min-height: 60px;
}

.summary-content h4 {
  margin: 0 0 8px 0;
  color: var(--text-primary);
  font-size: 14px;
}

.summary-text {
  white-space: pre-wrap;
  font-size: 13px;
  line-height: 1.5;
  color: var(--text-secondary);
}

.summary-meta {
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgba(255,255,255,0.1);
  font-size: 11px;
  color: var(--text-muted);
}

/* --- Chart Container --- */
.chart-container {
  height: 200px;
  margin-top: 12px;
  padding: 12px;
  background: rgba(0,0,0,0.2);
  border-radius: 8px;
}

/* --- Speaking Chart --- */
.speaking-chart {
  margin-top: 12px;
}

.speaking-bar {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
}

.speaking-name {
  min-width: 60px;
  color: var(--text-secondary);
  font-weight: 500;
}

.speaking-progress {
  flex: 1;
  height: 6px;
  background: rgba(255,255,255,0.1);
  border-radius: 3px;
  overflow: hidden;
}

.speaking-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--accent-2));
  border-radius: 3px;
  transition: width 0.3s ease;
}

.speaking-time {
  min-width: 40px;
  text-align: right;
  color: var(--text-muted);
  font-weight: 500;
}

/* --- Card Header --- */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-header h3 {
  margin: 0;
}

.btn-small {
  padding: 4px 8px;
  font-size: 11px;
  border: 1px solid rgba(255,255,255,0.2);
  background: transparent;
  color: var(--text-muted);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-small:hover {
  background: rgba(255,255,255,0.05);
  color: var(--text-secondary);
}

/* --- Debug Console --- */
.debug-console {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  max-width: 90vw;
  height: 400px;
  background: var(--bg);
  border: 1px solid var(--surface-light);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0,0,0,0.5);
  z-index: 1000;
  display: none;
}

.debug-console.visible {
  display: flex;
  flex-direction: column;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid var(--surface-light);
}

.debug-header h4 {
  margin: 0;
  color: var(--text-primary);
}

.btn-close {
  background: none;
  border: none;
  color: var(--text-muted);
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.btn-close:hover {
  background: rgba(255,255,255,0.1);
  color: var(--text-secondary);
}

.debug-content {
  flex: 1;
  overflow: auto;
  padding: 12px;
}

.debug-content pre {
  margin: 0;
  font-size: 12px;
  line-height: 1.4;
  color: var(--text-secondary);
  background: none;
}

/* --- Notifications --- */
.notifications-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.notification {
  padding: 12px 16px;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
  animation: slideIn 0.3s ease;
  max-width: 300px;
}

.notification.success {
  background: var(--success);
}

.notification.error {
  background: var(--danger);
}

.notification.warning {
  background: var(--warning);
}

.notification.info {
  background: var(--info);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* --- Participant Items --- */
.participant-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: rgba(255,255,255,0.02);
  border-radius: 8px;
  margin-bottom: 8px;
}

.participant-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.participant-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.participant-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.participant-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
}

.participant-status {
  font-size: 11px;
  color: var(--text-muted);
  display: flex;
  align-items: center;
  gap: 8px;
}

.connection-status {
  color: var(--success);
}

.attention-score {
  color: var(--accent-2);
  font-weight: 500;
}

.participant-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--warning);
}