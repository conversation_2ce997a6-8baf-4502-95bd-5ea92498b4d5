<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: white;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #8b5cf6;
        }
        .test-button {
            background: #8b5cf6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #7c3aed;
        }
        .result {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .sentiment-stats {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
            background: #333;
            border-radius: 6px;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
        }
        .positive { color: #10b981; }
        .neutral { color: #6b7280; }
        .negative { color: #ef4444; }
    </style>
</head>
<body>
    <h1>🧪 Frontend Fix Test Page</h1>
    <p>This page tests the fixes for the dashboard issues.</p>

    <div class="test-section">
        <h2>1. Sentiment Percentage Test</h2>
        <p>Testing if sentiment percentages update correctly:</p>
        <div class="sentiment-stats">
            <div class="stat-item">
                <div class="stat-label">Positive</div>
                <div class="stat-value positive" id="positivePercent">0%</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Neutral</div>
                <div class="stat-value neutral" id="neutralPercent">0%</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">Negative</div>
                <div class="stat-value negative" id="negativePercent">0%</div>
            </div>
        </div>
        <button class="test-button" onclick="testSentimentUpdate()">Test Sentiment Update</button>
        <div id="sentimentResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. Summary Generation Test</h2>
        <p>Testing if summary generation works:</p>
        <button class="test-button" id="generateSummaryBtn" onclick="testSummaryGeneration()">Generate Summary</button>
        <div id="summaryContent" class="result">
            <p>Click "Generate Summary" to test the functionality.</p>
        </div>
    </div>

    <div class="test-section">
        <h2>3. Transcript Download Test</h2>
        <p>Testing if transcript download works:</p>
        <button class="test-button" onclick="testTranscriptDownload()">Download Transcript</button>
        <div id="downloadResult" class="result"></div>
    </div>

    <script>
        const room = 'test-room';

        // Test sentiment percentage update
        function testSentimentUpdate() {
            const mockSentimentData = {
                sentiment_history: [
                    { score: 0.8, timestamp: Date.now() },
                    { score: -0.3, timestamp: Date.now() },
                    { score: 0.1, timestamp: Date.now() },
                    { score: 0.6, timestamp: Date.now() },
                    { score: -0.7, timestamp: Date.now() },
                    { score: 0.0, timestamp: Date.now() },
                    { score: 0.4, timestamp: Date.now() },
                    { score: -0.1, timestamp: Date.now() },
                    { score: 0.9, timestamp: Date.now() },
                    { score: -0.5, timestamp: Date.now() }
                ],
                overall_sentiment: 'neutral'
            };

            updateSentimentStats(mockSentimentData);
            document.getElementById('sentimentResult').innerHTML = 
                `<strong>✅ Test completed!</strong><br>
                Mock data: ${mockSentimentData.sentiment_history.length} sentiment entries<br>
                Check if percentages above updated correctly.`;
        }

        // Test summary generation
        async function testSummaryGeneration() {
            const button = document.getElementById('generateSummaryBtn');
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i>🔄</i> Generating...';
            button.disabled = true;
            
            try {
                const response = await fetch('/summarize', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ room })
                });
                
                const data = await response.json();
                
                if (response.ok && data.result) {
                    const formattedResult = data.result.replace(/\n/g, '<br>');
                    document.getElementById('summaryContent').innerHTML = `
                        <strong>✅ Summary Generated Successfully!</strong><br><br>
                        <div style="background: #444; padding: 10px; border-radius: 4px;">
                            ${formattedResult}
                        </div>
                        <br><small>Generated: ${new Date().toLocaleString()}</small>
                    `;
                } else {
                    document.getElementById('summaryContent').innerHTML = `
                        <strong>❌ Summary Generation Failed</strong><br>
                        Error: ${data.error || 'Unknown error'}
                    `;
                }
            } catch (error) {
                document.getElementById('summaryContent').innerHTML = `
                    <strong>❌ Network Error</strong><br>
                    ${error.message}
                `;
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
            }
        }

        // Test transcript download
        async function testTranscriptDownload() {
            try {
                const response = await fetch(`/transcript/${room}`);
                const data = await response.json();
                
                if (data.transcript && data.transcript.length > 0) {
                    const transcriptText = data.transcript.map(entry => 
                        `[${new Date(entry.ts * 1000).toLocaleTimeString()}] ${entry.speaker || 'Unknown'}: ${entry.text}`
                    ).join('\n');
                    
                    const blob = new Blob([transcriptText], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `test-transcript-${new Date().toISOString().split('T')[0]}.txt`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                    
                    document.getElementById('downloadResult').innerHTML = 
                        `<strong>✅ Download Successful!</strong><br>
                        Downloaded ${data.transcript.length} transcript entries.`;
                } else {
                    document.getElementById('downloadResult').innerHTML = 
                        `<strong>⚠️ No Data Available</strong><br>
                        No transcript data to download. Add some test data first.`;
                }
            } catch (error) {
                document.getElementById('downloadResult').innerHTML = 
                    `<strong>❌ Download Failed</strong><br>
                    Error: ${error.message}`;
            }
        }

        // Sentiment stats update function (copied from dashboard.js)
        function updateSentimentStats(sentimentData) {
            const history = sentimentData.sentiment_history || [];
            
            let positive = 0, neutral = 0, negative = 0;
            
            history.forEach(entry => {
                const score = entry.score || 0;
                if (score > 0.2) positive++;
                else if (score < -0.2) negative++;
                else neutral++;
            });
            
            const total = history.length;

            if (total > 0) {
                const positivePercent = Math.round((positive / total) * 100);
                const neutralPercent = Math.round((neutral / total) * 100);
                const negativePercent = Math.round((negative / total) * 100);
                
                document.getElementById('positivePercent').textContent = `${positivePercent}%`;
                document.getElementById('neutralPercent').textContent = `${neutralPercent}%`;
                document.getElementById('negativePercent').textContent = `${negativePercent}%`;
                
                console.log(`Sentiment percentages: ${positivePercent}% positive, ${neutralPercent}% neutral, ${negativePercent}% negative`);
            }
        }
    </script>
</body>
</html>
